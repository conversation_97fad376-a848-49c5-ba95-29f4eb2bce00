# Animal List Operations Program
# This program demonstrates basic list operations in Python

def main():
    print("Animal List Operations Demo")
    print("=" * 30)
    
    # Step 1: Create a list of four animals
    animals = ["lion", "elephant", "tiger", "giraffe"]
    print("Step 1: Create a list of four animals")
    print(f"Original list: {animals}")
    print(f"Number of animals: {len(animals)}")
    print()
    
    # Step 2: Remove the second animal in the list (index 1)
    print("Step 2: Remove the second animal in the list")
    print(f"The second animal is: '{animals[1]}'")
    removed_animal = animals.pop(1)  # Remove at index 1 (second position)
    print(f"Removed: {removed_animal}")
    print(f"List after removal: {animals}")
    print(f"Number of animals: {len(animals)}")
    print()
    
    # Step 3: Add cheetah to the list
    print("Step 3: Add cheetah to the list")
    animals.append("cheetah")
    print(f"Added: cheetah")
    print(f"List after adding cheetah: {animals}")
    print(f"Number of animals: {len(animals)}")
    print()
    
    # Step 4: Remove cheetah from the list
    print("Step 4: Remove cheetah from the list")
    animals.remove("cheetah")  # Remove by value
    print(f"Removed: cheetah")
    print(f"Final list: {animals}")
    print(f"Number of animals: {len(animals)}")
    print()
    
    # Summary
    print("Summary of operations:")
    print("1. Started with: ['lion', 'elephant', 'tiger', 'giraffe']")
    print("2. Removed 'elephant' (second animal)")
    print("3. Added 'cheetah'")
    print("4. Removed 'cheetah'")
    print(f"5. Final result: {animals}")

# Alternative methods demonstration
def demonstrate_alternative_methods():
    print("\n" + "=" * 50)
    print("BONUS: Alternative Methods for List Operations")
    print("=" * 50)
    
    # Create the same initial list
    animals = ["lion", "elephant", "tiger", "giraffe"]
    print(f"Starting list: {animals}")
    
    # Different ways to remove elements
    print("\nDifferent ways to remove elements:")
    
    # Method 1: del statement (remove by index)
    animals_copy1 = animals.copy()
    del animals_copy1[1]
    print(f"Using 'del animals[1]': {animals_copy1}")
    
    # Method 2: pop() without storing the result
    animals_copy2 = animals.copy()
    animals_copy2.pop(1)
    print(f"Using 'pop(1)': {animals_copy2}")
    
    # Method 3: remove() by value
    animals_copy3 = animals.copy()
    animals_copy3.remove("elephant")
    print(f"Using 'remove(\"elephant\")': {animals_copy3}")
    
    # Different ways to add elements
    print("\nDifferent ways to add elements:")
    
    # Method 1: append() - adds to the end
    animals_copy4 = animals_copy1.copy()
    animals_copy4.append("cheetah")
    print(f"Using 'append(\"cheetah\")': {animals_copy4}")
    
    # Method 2: insert() - adds at specific position
    animals_copy5 = animals_copy1.copy()
    animals_copy5.insert(0, "cheetah")  # Insert at beginning
    print(f"Using 'insert(0, \"cheetah\")': {animals_copy5}")
    
    # Method 3: extend() - adds multiple items
    animals_copy6 = animals_copy1.copy()
    animals_copy6.extend(["cheetah", "zebra"])
    print(f"Using 'extend([\"cheetah\", \"zebra\"])': {animals_copy6}")

# Run the program
if __name__ == "__main__":
    main()
    demonstrate_alternative_methods()
