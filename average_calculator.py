# Average Calculator using While Loop
# This program asks for numbers until -1 is entered, then calculates the average

print("Average Calculator")
print("Enter numbers one by one. Enter -1 to stop and calculate the average.")
print("-" * 60)

# Initialize variables
total = 0           # Sum of all numbers entered
count = 0           # Count of numbers entered (excluding -1)
number = 0          # Variable to store each number entered

# While loop to keep asking for numbers
while number != -1:
    # Ask user to enter a number
    number = float(input("Enter a number (-1 to stop): "))
    
    # Check if the number is not -1 before adding to total
    if number != -1:
        total = total + number  # Add to running total
        count = count + 1       # Increment counter
        print(f"Number entered: {number}, Running total: {total}, Count: {count}")

# Calculate and display the average (only if numbers were entered)
print("\n" + "=" * 40)
print("CALCULATION RESULTS")
print("=" * 40)

if count > 0:
    average = total / count
    print(f"Total numbers entered: {count}")
    print(f"Sum of all numbers: {total}")
    print(f"Average: {average}")
    print(f"Average (rounded to 2 decimal places): {average:.2f}")
else:
    print("No numbers were entered!")

print("\nThank you for using the Average Calculator!")
