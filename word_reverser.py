# Word Reverser Program
# This program asks the user for a 6-letter word and prints it backwards

def main():
    print("Welcome to the Word Reverser!")
    print("=" * 30)
    
    # Step 1: Ask the user to enter a 6-letter word
    word = input("Please enter a 6-letter word: ")
    
    # Step 2: Check if the word is exactly 6 letters
    if len(word) != 6:
        print(f"Oops! '{word}' has {len(word)} letters, but I need exactly 6 letters.")
        print("Please run the program again and try a different word.")
        return
    
    # Step 3: Reverse the word using string slicing
    reversed_word = word[::-1]
    
    # Step 4: Display the results
    print(f"\nOriginal word: {word}")
    print(f"Reversed word: {reversed_word}")
    
    # Bonus: Show each step of the reversal
    print(f"\nStep-by-step reversal:")
    for i in range(len(word)):
        print(f"Position {i + 1}: '{word[i]}' becomes position {len(word) - i}")
    
    print(f"\nFinal result: {word} → {reversed_word}")

# Run the program
if __name__ == "__main__":
    main()
